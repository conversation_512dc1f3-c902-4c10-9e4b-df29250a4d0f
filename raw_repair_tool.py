"""
RAW Photo Repair Tool
A comprehensive tool for detecting and repairing corrupted RAW image files
"""
import os
import sys
import rawpy
import numpy as np
from PIL import Image
import exifread
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from colorama import Fore, Style, init
from tqdm import tqdm
import utils

# Initialize colorama for colored output
init(autoreset=True)


class RAWRepairTool:
    """Main class for RAW photo repair operations"""

    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.supported_formats = {'.nef', '.cr2', '.cr3', '.arw', '.dng', '.orf', '.rw2', '.pef', '.srw', '.raf'}
        self.repair_stats = {
            'total_files': 0,
            'corrupted_files': 0,
            'repaired_files': 0,
            'failed_repairs': 0
        }

    def log(self, message: str, level: str = "INFO"):
        """Log messages with color coding"""
        if not self.verbose:
            return

        colors = {
            "INFO": Fore.WHITE,
            "SUCCESS": Fore.GREEN,
            "WARNING": Fore.YELLOW,
            "ERROR": Fore.RED
        }

        color = colors.get(level, Fore.WHITE)
        print(f"{color}[{level}] {message}{Style.RESET_ALL}")

    def check_file_integrity(self, file_path: str) -> Dict[str, Any]:
        """Check the integrity of a RAW file"""
        result = {
            'file_path': file_path,
            'is_valid': False,
            'file_size': 0,
            'can_read_raw': False,
            'has_exif': False,
            'exif_data': {},
            'errors': []
        }

        try:
            # Check file existence and size
            if not os.path.exists(file_path):
                result['errors'].append("File does not exist")
                return result

            result['file_size'] = os.path.getsize(file_path)
            if result['file_size'] < 1024:  # Too small for a RAW file
                result['errors'].append("File size too small for RAW format")
                return result

            # Try to read EXIF data
            try:
                with open(file_path, 'rb') as f:
                    tags = exifread.process_file(f, details=False)
                    if tags:
                        result['has_exif'] = True
                        result['exif_data'] = {str(key): str(tags[key]) for key in tags.keys()}
            except Exception as e:
                result['errors'].append(f"EXIF read error: {str(e)}")

            # Try to read RAW data
            try:
                with rawpy.imread(file_path) as raw:
                    # Basic validation - check if we can access raw data
                    raw_array = raw.raw_image
                    if raw_array is not None and raw_array.size > 0:
                        result['can_read_raw'] = True
                        result['is_valid'] = True
            except Exception as e:
                result['errors'].append(f"RAW read error: {str(e)}")

        except Exception as e:
            result['errors'].append(f"General error: {str(e)}")

        return result

    def attempt_repair(self, file_path: str, output_dir: str = "repaired") -> bool:
        """Attempt to repair a corrupted RAW file"""
        self.log(f"Attempting to repair: {file_path}")

        # Create backup
        backup_path = utils.create_backup(file_path)
        if not backup_path:
            self.log("Failed to create backup", "ERROR")
            return False

        try:
            # Ensure output directory exists
            utils.ensure_output_directory(output_dir)

            # Try different repair strategies in order of sophistication
            success = (
                self._repair_strategy_partial_corruption_fix(file_path, output_dir) or
                self._repair_strategy_basic_processing(file_path, output_dir) or
                self._repair_strategy_partial_read(file_path, output_dir) or
                self._repair_strategy_header_fix(file_path, output_dir)
            )

            if success:
                self.log(f"Successfully repaired: {file_path}", "SUCCESS")
                self.repair_stats['repaired_files'] += 1
                return True
            else:
                self.log(f"Failed to repair: {file_path}", "ERROR")
                self.repair_stats['failed_repairs'] += 1
                return False

        except Exception as e:
            self.log(f"Repair failed with exception: {str(e)}", "ERROR")
            self.repair_stats['failed_repairs'] += 1
            return False

    def _repair_strategy_partial_corruption_fix(self, file_path: str, output_dir: str) -> bool:
        """Advanced repair strategy for files with partial corruption"""
        try:
            self.log("Attempting partial corruption repair strategy", "INFO")

            # Read the file in binary mode to analyze corruption point
            with open(file_path, 'rb') as f:
                file_data = f.read()

            file_size = len(file_data)
            self.log(f"Original file size: {file_size:,} bytes", "INFO")

            # Try to find the corruption point by testing different truncation points
            corruption_points = []

            # Common corruption points to test (in bytes)
            # Start with points just before the known corruption point
            test_points = [
                4234789,  # Just before known corruption point
                4234000,  # Slightly before
                4230000,  # A bit more before
                4200000,  # 4.2MB
                4000000,  # 4MB
                3500000,  # 3.5MB
                3000000,  # 3MB
                2500000,  # 2.5MB
                2000000,  # 2MB
            ]

            for test_point in test_points:
                if test_point < file_size:
                    try:
                        # Create a temporary truncated file
                        temp_path = f"{file_path}.temp_truncated"
                        with open(temp_path, 'wb') as temp_f:
                            temp_f.write(file_data[:test_point])

                        # Test if this truncated version can be read
                        with rawpy.imread(temp_path) as raw:
                            raw_array = raw.raw_image
                            if raw_array is not None and raw_array.size > 0:
                                self.log(f"Found readable data up to byte {test_point:,}", "SUCCESS")

                                # Process the truncated RAW data
                                rgb = raw.postprocess(
                                    use_camera_wb=True,
                                    half_size=False,
                                    no_auto_bright=True,
                                    output_bps=16
                                )

                                # Save the recovered image
                                output_path = os.path.join(output_dir, f"{Path(file_path).stem}_partial_recovered.tiff")
                                Image.fromarray(rgb).save(output_path)
                                self.log(f"Saved partially recovered image: {output_path}", "SUCCESS")

                                # Clean up temp file
                                os.remove(temp_path)
                                return True

                        # Clean up temp file if test failed
                        os.remove(temp_path)

                    except Exception as e:
                        # Clean up temp file on error
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                        continue

            # If truncation didn't work, try to extract embedded JPEG preview
            return self._extract_embedded_preview(file_path, output_dir)

        except Exception as e:
            self.log(f"Partial corruption repair failed: {str(e)}", "WARNING")
            return False

    def _extract_embedded_preview(self, file_path: str, output_dir: str) -> bool:
        """Extract all embedded JPEG previews from NEF file"""
        try:
            self.log("Attempting to extract all embedded JPEG previews", "INFO")

            with open(file_path, 'rb') as f:
                data = f.read()

            extracted_count = 0
            soi_marker = b'\xff\xd8\xff'  # JPEG SOI marker
            eoi_marker = b'\xff\xd9'      # JPEG EOI marker

            pos = 0
            while True:
                # Find JPEG start
                jpeg_start = data.find(soi_marker, pos)
                if jpeg_start == -1:
                    break

                # Find corresponding end
                jpeg_end = data.find(eoi_marker, jpeg_start)
                if jpeg_end == -1:
                    pos = jpeg_start + 1
                    continue

                jpeg_end += 2  # Include the EOI marker
                jpeg_data = data[jpeg_start:jpeg_end]
                size = len(jpeg_data)

                # Determine JPEG type based on size
                if size < 50000:
                    jpeg_type = "thumbnail"
                elif size < 500000:
                    jpeg_type = "preview"
                else:
                    jpeg_type = "full_preview"

                output_path = os.path.join(output_dir, f"{Path(file_path).stem}_{jpeg_type}_{extracted_count + 1}.jpg")

                try:
                    with open(output_path, 'wb') as out_f:
                        out_f.write(jpeg_data)

                    # Verify the extracted JPEG
                    Image.open(output_path)
                    self.log(f"Extracted {jpeg_type}: {output_path} ({size:,} bytes)", "SUCCESS")
                    extracted_count += 1

                except Exception as e:
                    if os.path.exists(output_path):
                        os.remove(output_path)
                    self.log(f"Failed to extract JPEG at position {jpeg_start}: {e}", "WARNING")

                pos = jpeg_end

            if extracted_count > 0:
                self.log(f"Successfully extracted {extracted_count} JPEG images", "SUCCESS")
                return True
            else:
                self.log("No valid JPEG images found", "WARNING")
                return False

        except Exception as e:
            self.log(f"JPEG preview extraction failed: {str(e)}", "WARNING")
            return False

    def _repair_strategy_basic_processing(self, file_path: str, output_dir: str) -> bool:
        """Basic repair strategy using rawpy processing"""
        try:
            with rawpy.imread(file_path) as raw:
                # Try basic postprocessing
                rgb = raw.postprocess(
                    use_camera_wb=True,
                    half_size=False,
                    no_auto_bright=True,
                    output_bps=16
                )

                # Save as TIFF
                output_path = os.path.join(output_dir, f"{Path(file_path).stem}_repaired.tiff")
                Image.fromarray(rgb).save(output_path)
                self.log(f"Saved repaired image: {output_path}", "SUCCESS")
                return True

        except Exception as e:
            self.log(f"Basic processing failed: {str(e)}", "WARNING")
            return False

    def _repair_strategy_partial_read(self, file_path: str, output_dir: str) -> bool:
        """Attempt partial read and reconstruction"""
        try:
            with rawpy.imread(file_path) as raw:
                # Try to get raw image data
                raw_array = raw.raw_image

                if raw_array is not None:
                    # Apply basic demosaicing
                    if len(raw_array.shape) == 2:
                        # Convert to 3-channel using simple interpolation
                        height, width = raw_array.shape
                        rgb_array = np.zeros((height, width, 3), dtype=np.uint16)

                        # Simple demosaicing (Bayer pattern assumption)
                        rgb_array[:, :, 1] = raw_array  # Green channel
                        rgb_array[::2, ::2, 0] = raw_array[::2, ::2]  # Red
                        rgb_array[1::2, 1::2, 2] = raw_array[1::2, 1::2]  # Blue

                        # Normalize and convert
                        rgb_array = (rgb_array / np.max(rgb_array) * 65535).astype(np.uint16)

                        output_path = os.path.join(output_dir, f"{Path(file_path).stem}_partial_repair.tiff")
                        Image.fromarray(rgb_array).save(output_path)
                        self.log(f"Saved partially repaired image: {output_path}", "SUCCESS")
                        return True

        except Exception as e:
            self.log(f"Partial read failed: {str(e)}", "WARNING")
            return False

    def _repair_strategy_header_fix(self, file_path: str, output_dir: str) -> bool:
        """Attempt to fix file header issues"""
        try:
            # Read file in binary mode and attempt header reconstruction
            with open(file_path, 'rb') as f:
                data = f.read()

            # Look for TIFF header patterns that might indicate recoverable data
            tiff_headers = [b'II*\x00', b'MM\x00*']

            for header in tiff_headers:
                pos = data.find(header)
                if pos > 0:
                    # Found potential TIFF data, try to extract
                    tiff_data = data[pos:]

                    output_path = os.path.join(output_dir, f"{Path(file_path).stem}_header_fixed.tiff")
                    with open(output_path, 'wb') as out_f:
                        out_f.write(tiff_data)

                    # Verify the extracted file
                    try:
                        Image.open(output_path)
                        self.log(f"Saved header-fixed image: {output_path}", "SUCCESS")
                        return True
                    except:
                        os.remove(output_path)
                        continue

        except Exception as e:
            self.log(f"Header fix failed: {str(e)}", "WARNING")
            return False

    def process_directory(self, input_dir: str, output_dir: str = "repaired") -> Dict[str, Any]:
        """Process all RAW files in a directory"""
        self.log(f"Processing directory: {input_dir}")

        raw_files = utils.find_raw_files(input_dir)
        self.repair_stats['total_files'] = len(raw_files)

        if not raw_files:
            self.log("No RAW files found in directory", "WARNING")
            return self.repair_stats

        self.log(f"Found {len(raw_files)} RAW files")

        # Process each file with progress bar
        for file_path in tqdm(raw_files, desc="Processing files"):
            integrity = self.check_file_integrity(file_path)

            if not integrity['is_valid']:
                self.log(f"Corrupted file detected: {file_path}", "WARNING")
                self.repair_stats['corrupted_files'] += 1
                self.attempt_repair(file_path, output_dir)
            else:
                self.log(f"File OK: {file_path}", "SUCCESS")

        return self.repair_stats

    def print_summary(self):
        """Print repair summary"""
        print(f"\n{Fore.CYAN}=== REPAIR SUMMARY ==={Style.RESET_ALL}")
        print(f"Total files processed: {self.repair_stats['total_files']}")
        print(f"Corrupted files found: {self.repair_stats['corrupted_files']}")
        print(f"Successfully repaired: {self.repair_stats['repaired_files']}")
        print(f"Failed repairs: {self.repair_stats['failed_repairs']}")

        if self.repair_stats['corrupted_files'] > 0:
            success_rate = (self.repair_stats['repaired_files'] / self.repair_stats['corrupted_files']) * 100
            print(f"Repair success rate: {success_rate:.1f}%")
