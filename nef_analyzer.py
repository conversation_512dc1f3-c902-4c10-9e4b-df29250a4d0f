#!/usr/bin/env python3
"""
NEF File Analyzer - Specialized tool for analyzing and recovering data from corrupted NEF files
"""
import os
import struct
from typing import List, Tu<PERSON>, Dict, Optional

class NEFAnalyzer:
    """Analyzer for NEF (Nikon Electronic Format) files"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        self.file_size = 0
        self.load_file()
    
    def load_file(self):
        """Load the NEF file into memory"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            self.file_size = len(self.file_data)
            print(f"Loaded NEF file: {self.file_size:,} bytes")
        except Exception as e:
            print(f"Error loading file: {e}")
            raise
    
    def find_tiff_headers(self) -> List[Tuple[int, str]]:
        """Find all TIFF headers in the file"""
        headers = []
        
        # TIFF headers
        patterns = [
            (b'II*\x00', 'Little Endian TIFF'),
            (b'MM\x00*', 'Big Endian TIFF'),
        ]
        
        for pattern, description in patterns:
            pos = 0
            while True:
                pos = self.file_data.find(pattern, pos)
                if pos == -1:
                    break
                headers.append((pos, description))
                pos += 1
        
        return sorted(headers)
    
    def find_jpeg_images(self) -> List[Tuple[int, int, str]]:
        """Find all JPEG images embedded in the file"""
        jpegs = []
        
        # JPEG markers
        soi_marker = b'\xff\xd8\xff'  # Start of Image
        eoi_marker = b'\xff\xd9'      # End of Image
        
        pos = 0
        while True:
            # Find JPEG start
            start_pos = self.file_data.find(soi_marker, pos)
            if start_pos == -1:
                break
            
            # Find corresponding end
            end_pos = self.file_data.find(eoi_marker, start_pos)
            if end_pos == -1:
                # No end marker found, skip this one
                pos = start_pos + 1
                continue
            
            end_pos += 2  # Include the EOI marker
            size = end_pos - start_pos
            
            # Determine JPEG type based on size
            if size < 50000:
                jpeg_type = "Thumbnail"
            elif size < 500000:
                jpeg_type = "Preview"
            else:
                jpeg_type = "Full Preview"
            
            jpegs.append((start_pos, end_pos, jpeg_type))
            pos = end_pos
        
        return jpegs
    
    def extract_jpeg(self, start_pos: int, end_pos: int, output_path: str) -> bool:
        """Extract a JPEG image from the file"""
        try:
            jpeg_data = self.file_data[start_pos:end_pos]
            with open(output_path, 'wb') as f:
                f.write(jpeg_data)
            return True
        except Exception as e:
            print(f"Error extracting JPEG: {e}")
            return False
    
    def find_corruption_point(self) -> Optional[int]:
        """Try to identify the exact corruption point"""
        # Look for patterns that might indicate corruption
        corruption_indicators = [
            b'\x00' * 100,  # Long sequences of null bytes
            b'\xff' * 100,  # Long sequences of 0xFF
        ]
        
        earliest_corruption = None
        
        for pattern in corruption_indicators:
            pos = self.file_data.find(pattern)
            if pos != -1:
                if earliest_corruption is None or pos < earliest_corruption:
                    earliest_corruption = pos
        
        return earliest_corruption
    
    def analyze_file_structure(self) -> Dict:
        """Analyze the overall file structure"""
        analysis = {
            'file_size': self.file_size,
            'tiff_headers': self.find_tiff_headers(),
            'jpeg_images': self.find_jpeg_images(),
            'corruption_point': self.find_corruption_point(),
        }
        
        return analysis
    
    def extract_all_recoverable_images(self, output_dir: str) -> List[str]:
        """Extract all recoverable images from the NEF file"""
        os.makedirs(output_dir, exist_ok=True)
        extracted_files = []
        
        jpegs = self.find_jpeg_images()
        
        for i, (start_pos, end_pos, jpeg_type) in enumerate(jpegs):
            filename = f"{os.path.splitext(os.path.basename(self.file_path))[0]}_{jpeg_type.lower().replace(' ', '_')}_{i+1}.jpg"
            output_path = os.path.join(output_dir, filename)
            
            if self.extract_jpeg(start_pos, end_pos, output_path):
                print(f"Extracted {jpeg_type}: {output_path} ({end_pos - start_pos:,} bytes)")
                extracted_files.append(output_path)
            else:
                print(f"Failed to extract {jpeg_type}")
        
        return extracted_files
    
    def print_analysis(self):
        """Print detailed analysis of the file"""
        analysis = self.analyze_file_structure()
        
        print(f"\n=== NEF File Analysis: {os.path.basename(self.file_path)} ===")
        print(f"File size: {analysis['file_size']:,} bytes")
        
        print(f"\nTIFF Headers found: {len(analysis['tiff_headers'])}")
        for pos, desc in analysis['tiff_headers']:
            print(f"  {pos:8,}: {desc}")
        
        print(f"\nJPEG Images found: {len(analysis['jpeg_images'])}")
        for start, end, jpeg_type in analysis['jpeg_images']:
            size = end - start
            print(f"  {start:8,} - {end:8,}: {jpeg_type} ({size:,} bytes)")
        
        if analysis['corruption_point']:
            print(f"\nPossible corruption point: {analysis['corruption_point']:,}")
        else:
            print(f"\nNo obvious corruption patterns detected")


def main():
    """Main function for command line usage"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python nef_analyzer.py <nef_file>")
        sys.exit(1)
    
    nef_file = sys.argv[1]
    
    if not os.path.exists(nef_file):
        print(f"Error: File '{nef_file}' not found")
        sys.exit(1)
    
    try:
        analyzer = NEFAnalyzer(nef_file)
        analyzer.print_analysis()
        
        # Extract all recoverable images
        output_dir = f"recovered_{os.path.splitext(os.path.basename(nef_file))[0]}"
        extracted = analyzer.extract_all_recoverable_images(output_dir)
        
        print(f"\nExtracted {len(extracted)} images to '{output_dir}' directory")
        
    except Exception as e:
        print(f"Error analyzing file: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
